# WhatsApp Backup Checker

A modern web application to scan and identify WhatsApp backup files on your device. Supports both Android and iOS backup formats with bilingual interface (English/Arabic).

## 🌟 Features

### Multi-Platform Support
- **Android**: Detects `.db`, `.crypt12`, `.crypt14`, `msgstore.db` files
- **iOS**: Detects `.sqlite`, `.plist`, `.mddata`, `.mdinfo` files and iTunes backup formats
- **Cross-platform**: Supports backup archives (`.zip`, `.tar`) and cloud storage files

### Smart Detection
- **High Confidence**: Files that are definitely WhatsApp backups
- **Medium Confidence**: Files that might be related to WhatsApp
- **Pattern Matching**: Uses comprehensive regex patterns for accurate detection
- **iOS Bundle Detection**: Recognizes iOS app bundle identifiers and backup structures

### Bilingual Interface
- **English**: Full English interface
- **Arabic**: Complete Arabic translation with RTL support
- **Language Toggle**: Easy switching between languages
- **Localized Dates**: Date formatting based on selected language

### User-Friendly Interface
- **Drag & Drop**: Easy file and folder selection
- **Real-time Scanning**: Progress indicators during file analysis
- **Detailed Results**: File size, modification date, and confidence levels
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation
```bash
# Clone or download the project
cd whatsapp-backup-checker

# Install dependencies
npm install

# Start development server
npm run dev
```

### Usage
1. Open your browser to `http://localhost:5173/`
2. Choose your preferred language (EN/العربية)
3. Select files or folders to scan
4. View detailed results with confidence levels

## 📱 Supported File Types

### Android WhatsApp Backups
- `msgstore.db.crypt12` - Encrypted message database
- `msgstore.db.crypt14` - Newer encrypted format
- `wa.db` - WhatsApp database
- `WhatsApp.db` - Main database file
- `.nomedia` - WhatsApp folder marker

### iOS WhatsApp Backups
- `ChatStorage.sqlite` - iOS message database
- `*.plist` - Property list files
- `*.mddata` - iOS backup metadata
- `Manifest.plist` - iTunes backup manifest
- Bundle identifier: `net.whatsapp.WhatsApp`

### Common Locations
- **Android**: `/WhatsApp/Databases/`
- **iOS**: iTunes/Finder backups, iCloud files
- **iOS Path**: `~/Library/Application Support/MobileSync/Backup/`
- **Cloud**: Google Drive, iCloud backup files

## 🔒 Privacy & Security

- **Client-side Processing**: All file scanning happens in your browser
- **No Upload**: Files are never uploaded to any server
- **Local Analysis**: Uses browser File API for secure file access
- **No Data Collection**: No personal data is collected or stored

## 🛠️ Technical Details

- **Framework**: React 18 with Vite
- **Styling**: Custom CSS with RTL support
- **File Processing**: Browser File API
- **Pattern Matching**: Comprehensive regex patterns
- **Internationalization**: Built-in English/Arabic support

## 📄 License

This project is open source and available under the MIT License.
