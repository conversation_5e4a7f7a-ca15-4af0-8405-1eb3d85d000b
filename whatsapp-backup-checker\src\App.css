.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.app.rtl {
  direction: rtl;
  font-family: 'Segoe UI', '<PERSON>hom<PERSON>', 'Arial', sans-serif;
}

.app.ltr {
  direction: ltr;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.language-switcher {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.app.rtl .language-switcher {
  right: auto;
  left: 0;
}

.lang-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #25D366;
  background: white;
  color: #25D366;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.lang-btn:hover {
  background: #f0f8f0;
}

.lang-btn.active {
  background: #25D366;
  color: white;
}

.app-header h1 {
  color: #25D366;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.app-header p {
  color: #666;
  font-size: 1.1rem;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.upload-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  border: 2px dashed #ddd;
  text-align: center;
}

.upload-section h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.upload-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.upload-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-btn {
  background: #25D366;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  display: inline-block;
  text-decoration: none;
}

.upload-btn:hover {
  background: #128C7E;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.file-info {
  margin-top: 1rem;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.reset-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.reset-btn:hover {
  background: #c82333;
}

.scanning {
  text-align: center;
  padding: 2rem;
  background: #fff3cd;
  border-radius: 8px;
  border: 1px solid #ffeaa7;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #25D366;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.results-section h2 {
  color: #333;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #25D366;
  padding-bottom: 0.5rem;
}

.results-summary {
  margin-bottom: 2rem;
}

.summary-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #25D366;
}

.summary-card h3 {
  margin-bottom: 1rem;
  color: #333;
}

.summary-card p {
  margin: 0.5rem 0;
  color: #666;
}

.backup-files, .suspicious-files {
  margin-bottom: 2rem;
}

.backup-files h3 {
  color: #28a745;
  margin-bottom: 1rem;
}

.suspicious-files h3 {
  color: #ffc107;
  margin-bottom: 1rem;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.app.rtl .file-item {
  flex-direction: row-reverse;
}

.file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.file-item.confirmed {
  background: #d4edda;
  border-color: #c3e6cb;
}

.file-item.suspicious {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.file-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
}

.app.rtl .file-details {
  text-align: right;
}

.file-details h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.file-path {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  word-break: break-all;
}

.file-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.85rem;
  color: #888;
}

.app.rtl .file-meta {
  flex-direction: row-reverse;
  text-align: right;
}

.confidence {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.confidence.high {
  background: #d4edda;
  color: #155724;
}

.confidence.medium {
  background: #fff3cd;
  color: #856404;
}

.no-results {
  text-align: center;
  padding: 2rem;
  background: #f8d7da;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.no-results h3 {
  color: #721c24;
  margin-bottom: 1rem;
}

.tips {
  text-align: left;
  margin-top: 1.5rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
}

.app.rtl .tips {
  text-align: right;
}

.tips h4 {
  color: #333;
  margin-bottom: 1rem;
}

.tips ul {
  color: #666;
  line-height: 1.6;
}

.info-section {
  background: #e9ecef;
  padding: 2rem;
  border-radius: 12px;
  margin-top: 2rem;
}

.info-section h3 {
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
  color: #25D366;
  margin-bottom: 1rem;
}

.info-card ul {
  color: #666;
  line-height: 1.6;
}

.app.rtl .info-card ul {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 0;
}

.info-card li {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .upload-buttons {
    flex-direction: column;
    align-items: center;
  }

  .file-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
